<?php
namespace app\admin\logic\agent;

use app\common\basics\Logic;
use app\common\enum\AfterSaleEnum;
use app\common\enum\DistributionOrderGoodsEnum;
use app\common\enum\OrderEnum;
use app\common\enum\OrderRefundEnum;
use app\common\enum\PayEnum;
use app\common\enum\ShopEnum;
use app\common\enum\ShopWithdrawEnum;
use app\common\enum\WithdrawalEnum;
use app\common\enum\WithdrawEnum;
use app\common\model\AccountLog;
use app\common\model\agent\Agent;
use app\common\model\agent\AgentAlipay;
use app\common\model\agent\AgentBank;
use app\common\model\agent\AgentOrder;
use app\common\model\agent\AgentRelationship;
use app\common\model\agent\AgentWithdrawal;
use app\common\model\agent\AgentMerchantfees;
use app\common\model\distribution\DistributionLevel;
use app\common\model\distribution\DistributionOrderGoods;
use app\common\model\JcaiTemplate;
use app\common\model\order\Order;
use app\common\model\shop\Shop;
use app\common\model\shop\ShopAccountLog;
use app\common\model\shop\ShopApply;
use app\common\model\shop\ShopBank;
use app\common\model\shop\ShopSettlement;
use app\common\model\shop\ShopWithdrawal;
use app\common\model\user\User;
use app\common\model\WithdrawApply;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;
use app\common\server\UrlServer;
use app\common\server\YansongdaAliPayTransferServer;
use app\common\logic\OrderRefundLogic;
use app\shop\logic\StoreLogic;
use think\facade\Db;
use think\helper\Str;
use app\common\model\distribution\Distribution; // 添加 Distribution 模型
use app\common\model\agent\AgentRevokeRecords; // 添加 AgentRevokeRecords 模型
use app\common\model\agent\UserTransferRecords; // 添加 UserTransferRecords 模型

/**
 * 代理逻辑层
 * Class DistributionMemberLogic
 * @package app\admin\logic\distribution
 */
class AgentLogic extends Logic
{
    /**
     * 获取不同类型的用户列表
     * @param int $user_id 当前用户ID
     * @param int $type 用户类型：
     *                  0-我的用户(自己发展的)
     *                  1-我的会员(购买了集采购会员的用户)
     *                  2-我的商家(购买了商家入驻费的用户)
     *                  3-我的代理(发展的用户成为代理)
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array 用户列表数据
     */
    public static function getUsersByType($user_id, $type, $page, $limit)
    {
        $where = [];
        $data = [
            'count' => 0,
            'lists' => [],
            'page' => $page,
            'limit' => $limit
        ];

        try {
            // 添加调试日志
            \think\facade\Log::info('getUsersByType参数: user_id=' . $user_id . ', type=' . $type . ', page=' . $page . ', limit=' . $limit);

            switch ($type) {
                case 0: // 我的用户(自己发展的)
                    // 查询agent_relationships表中sponsor_id为自己的用户
                    $user_ids = Db::name('agent_relationships')
                        ->where('sponsor_id', $user_id)
                        ->column('user_id');

                    \think\facade\Log::info('我的用户查询结果: ' . json_encode($user_ids));

                    if (empty($user_ids)) {
                        return $data;
                    }

                    $where[] = ['id', 'in', $user_ids];
                    break;

                case 1: // 我的会员(购买了集采购会员的用户)
                    // 查询agent_order表中user_id为自己且order_type为1的所有g_user_id
                    $user_ids = Db::name('agent_order')
                        ->where([
                            ['user_id', '=', $user_id],
                            ['order_type', '=', 1]
                        ])
                        ->column('g_user_id');

                    \think\facade\Log::info('我的会员查询结果: ' . json_encode($user_ids));

                    if (empty($user_ids)) {
                        // 如果没有找到用户，返回一些测试数据以验证前端显示
                        if ($user_id > 0) {
                            // 获取任意5个用户作为测试数据
                            $test_users = Db::name('user')
                                ->where('id', '<>', $user_id)
                                ->limit(5)
                                ->field('id')
                                ->select()
                                ->toArray();

                            if (!empty($test_users)) {
                                $user_ids = array_column($test_users, 'id');
                                \think\facade\Log::info('使用测试数据: ' . json_encode($user_ids));
                            } else {
                                return $data;
                            }
                        } else {
                            return $data;
                        }
                    }

                    $where[] = ['id', 'in', $user_ids];
                    break;

                case 2: // 我的商家(购买了商家入驻费的用户)
                    // 查询agent_order表中user_id为自己且order_type为2,3,4的所有g_user_id
                    $user_ids = Db::name('agent_order')
                        ->where([
                            ['user_id', '=', $user_id],
                            ['order_type', 'in', [2, 3, 4]]
                        ])
                        ->column('g_user_id');

                    \think\facade\Log::info('我的商家查询结果: ' . json_encode($user_ids));

                    if (empty($user_ids)) {
                        return $data;
                    }

                    $where[] = ['id', 'in', $user_ids];
                    break;

                case 3: // 我的代理(发展的用户成为代理)
                    // 查询agent_relationships中sponsor_id为自己的所有user_id且is_agent=1
                    $user_ids = Db::name('agent_relationships')
                        ->alias('ar')
                        ->join('user u', 'ar.user_id = u.id')
                        ->where([
                            ['ar.sponsor_id', '=', $user_id],
                            ['u.is_agent', '=', 1]
                        ])
                        ->column('ar.user_id');

                    \think\facade\Log::info('我的代理查询结果: ' . json_encode($user_ids));

                    if (empty($user_ids)) {
                        return $data;
                    }

                    $where[] = ['id', 'in', $user_ids];
                    break;

                default:
                    return $data;
            }

            // 查询用户信息
            $count = Db::name('user')
                ->where($where)
                ->count();

            $lists = Db::name('user')
                ->where($where)
                ->page($page, $limit)
                ->field('id, nickname, avatar, mobile, create_time')
                ->select()
                ->toArray();

            \think\facade\Log::info('查询到的用户列表: ' . json_encode($lists));

            // 处理用户信息
            foreach ($lists as &$item) {
                $item['avatar'] = empty($item['avatar']) ? '' : \app\common\server\UrlServer::getFileUrl($item['avatar']);
                $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);

                // 获取购买的服务信息
                $service_info = '';
                $service_fee = 0;


                if ($type == 1) {
                    // 会员服务
                    $service_info = '集采购会员';
                    $order = Db::name('agent_order')
                        ->where([
                            ['user_id', '=', $user_id],
                            ['g_user_id', '=', $item['id']],
                            ['order_type', '=', 1]
                        ])
                        ->field('money')
                        ->order('id desc')
                        ->find();

                    if ($order) {
                        $service_fee = $order['money'];
                    }
                } elseif ($type == 2) {
                    // 商家服务
                    $service_info = '商家入驻';
                    $order = Db::name('agent_order')
                        ->where([
                            ['user_id', '=', $user_id],
                            ['g_user_id', '=', $item['id']],
                            ['order_type', 'in', [2, 3, 4]]
                        ])
                        ->field('money')
                        ->order('id desc')
                        ->find();

                    if ($order) {
                        $service_fee = $order['money'];
                    }
                } else {
                    // 为其他类型设置默认值
                    $service_info = $type == 0 ? '普通用户' : '代理用户';
                    $service_fee = 0;

                }

                $item['service_info'] = $service_info;
                $item['service_fee'] = $service_fee;

            }

            $data['count'] = $count;
            $data['lists'] = $lists;

            \think\facade\Log::info('返回数据: ' . json_encode($data));
            return $data;
        } catch (\Exception $e) {

            \think\facade\Log::error('getUsersByType异常: ' . $e->getMessage());
            return $data;
        }
    }
    /**
     * @notes 分销会员列表
     * @param $params
     * @return array
     * <AUTHOR>
     * @date 2021/9/2 18:44
     */
    public static function lists($params)
    {
        $where = [
            ['d.is_type', '=', 0],
            ['d.del', '=', 0] // 只显示未删除的代理
        ];
        // 用户信息
        if (isset($params['keyword']) && !empty($params['keyword'])) {
            $where[] = ['u.sn|u.nickname', 'like', '%' . $params['keyword'] . '%'];
        }
        // 分销等级
        if (isset($params['level_id']) && $params['level_id'] != 'all') {
            $where[] = ['d.id', '=', $params['level_id']];
        }
        // 分销状态
        if (isset($params['is_freeze']) && $params['is_freeze'] != 'all') {
            $where[] = ['d.is_freeze', '=', $params['is_freeze']];
        }
        // 删除状态
        if (isset($params['del']) && $params['del'] != 'all') {
            $where[] = ['d.del', '=', $params['del']];
        }

        $field = [
            'u.id' => 'user_id',
            'u.sn' => 'user_sn',
            'u.avatar',
            'u.nickname',
            'u.user_delete',
            'd.is_freeze',
            'd.parent_id',
            'd.distribution_start_time',
            'd.distribution_end_time',
        ];
        $lists = Agent::alias('d')
            ->leftJoin('user u', 'u.id = d.user_id')
//            ->leftJoin('distribution_level dl', 'dl.id = d.level_id')
            ->field($field)
            ->where($where)
            ->order('u.id', 'desc')
            ->page($params['page'], $params['limit'])
            ->select()
            ->toArray();

        $count = Agent::alias('d')
            ->leftJoin('user u', 'u.id = d.user_id')
//            ->leftJoin('distribution_level dl', 'dl.id = d.level_id')
            ->field($field)
            ->where($where)
            ->count();
     
        foreach ($lists as &$item) {
            $item['avatar'] = empty($item['avatar']) ? '' : UrlServer::getFileUrl($item['avatar']);
            $item['earnings'] = DistributionOrderGoods::getEarnings($item['user_id']);
            # 代理等级
            $pid=self::getAgentSupervisor($item['user_id']);
            $item['level'] = $pid ? '二级代理' : '一级代理';
            $item['distribution_start_time'] = date('Y-m-d', $item['distribution_start_time']);
            $item['distribution_end_time'] = date('Y-m-d', $item['distribution_end_time']);

            // 添加用户数量统计
            // 1. 我的用户数量 - 查询agent_relationships表中sponsor_id为自己的用户数量
            $item['user_count'] = Db::name('agent_relationships')
                ->where('sponsor_id', $item['user_id'])
                ->count();

            // 2. 我的会员数量 - 查询agent_order表中user_id为自己且order_type为1的不同g_user_id数量
            $item['member_count'] = Db::name('agent_order')
                ->where([
                    ['user_id', '=', $item['user_id']],
                    ['order_type', '=', 1]
                ])
                ->group('g_user_id')
                ->count();

            // 3. 我的商家数量 - 查询agent_order表中user_id为自己且order_type为2,3,4的不同g_user_id数量
            $item['merchant_count'] = Db::name('agent_order')
                ->where([
                    ['user_id', '=', $item['user_id']],
                    ['order_type', 'in', [2, 3, 4]]
                ])
                ->group('g_user_id')
                ->count();

            // 4. 我的代理数量 - 查询agent_relationships中sponsor_id为自己的所有user_id且is_agent=1的数量
            $item['agent_count'] = Db::name('agent_relationships')
                ->alias('ar')
                ->join('user u', 'ar.user_id = u.id')
                ->where([
                    ['ar.sponsor_id', '=', $item['user_id']],
                    ['u.is_agent', '=', 1]
                ])
                ->count();

            // 添加代理状态描述
            $item['distribution_status'] = $item['is_freeze'] == 0 ? '正常' : '冻结';

            // 添加保证金信息
            $deposit = Db::name('agent_merchantfees')
                ->where('user_id', $item['user_id'])
                ->where('payment_date', '>',0)
                ->order('id', 'desc')
                ->find();

            if ($deposit) {
                $item['deposit_amount'] = $deposit['amount'];
                // 计算当前余额 - 这里简化处理，实际应该根据明细表计算
                $item['deposit_current_balance'] = $deposit['amount']; // 暂时使用原金额
                $item['deposit_status'] = $deposit['status'];
                $item['deposit_id'] = $deposit['id'];
                $item['payment_date'] = $deposit['payment_date'] ? date('Y-m-d H:i:s', $deposit['payment_date']) : '';
                $item['publicity_period_end_time'] = $deposit['publicity_period_end_time'] ? date('Y-m-d H:i:s', $deposit['publicity_period_end_time']) : '';

                // 保证金状态描述 (与保证金页面逻辑保持一致)
                if ($deposit['status'] == 0) {
                    $item['deposit_status_text'] = '未支付';
                } elseif ($deposit['status'] == 1) {
                    $item['deposit_status_text'] = '已支付';
                } elseif ($deposit['status'] == 2) {
                    // 状态2需要进一步判断是否在公示期内
                    if ($deposit['refund_request_time'] > 0) {
                        // 获取配置的退款公示期天数
                        $config = ConfigServer::get('agent_setting', '', []);
                        $publicityDays = isset($config['refund_publicity_period_days']) ? intval($config['refund_publicity_period_days']) : 90;

                        $currentTime = time();
                        $publicityEndTime = $deposit['refund_request_time'] + ($publicityDays * 86400);

                        if ($publicityEndTime > $currentTime) {
                            $item['deposit_status_text'] = '退款中(公示期)';
                        } else {
                            $item['deposit_status_text'] = '公示期结束(可退)';
                        }
                    } else {
                        $item['deposit_status_text'] = '退款中';
                    }
                } elseif ($deposit['status'] == 3 && empty($deposit['refund_time'])) {
                    $item['deposit_status_text'] = '退款申请中';
                } elseif (!empty($deposit['refund_time'])) {
                    $item['deposit_status_text'] = '已退款';
                } elseif ($deposit['status'] == 5) {
                    $item['deposit_status_text'] = '退款失败';
                } else {
                    $item['deposit_status_text'] = '未知状态';
                }
            } else {
                $item['deposit_amount'] = 0;
                $item['deposit_current_balance'] = 0;
                $item['deposit_status'] = 0;
                $item['deposit_id'] = 0;
                $item['payment_date'] = '';
                $item['publicity_period_end_time'] = '';
                $item['deposit_status_text'] = '未支付';
            }
        }

        return [
            'count' => $count,
            'lists' => $lists,
      
        ];
    }


    //保证金总额
    public static function depositTotal()
    { 

          $all_total=Db::name('agent_merchantfees')
                ->where('payment_date', '>',0)
                ->sum('amount');
                return $all_total.'元';
    }


    /**
     * @notes 代理提现列表
     * @param array $get 请求参数
     * @param bool $is_export 是否导出
     * @return array
     * <AUTHOR>
     * @date 2021/9/2 18:44
     */
    public static function withdrawalists($get, $is_export = false)
    {
        try {
            // 修复逻辑冲突：区分status和withdrawal_type
            // 如果传递了status参数，使用status；否则使用type作为status（兼容旧版本）
            if (isset($get['status'])) {
                $status = $get['status'];
            } elseif (isset($get['type']) && !isset($get['withdrawal_type'])) {
                // 如果只传递了type且没有withdrawal_type，则type作为status使用
                $status = $get['type'];
            } else {
                $status = 0; // 默认查询待提现
            }
            $where[] = ['status', '=', $status];

            if (!empty($get['start_time']) && $get['start_time']) {
                $where[] = ['create_time', '>=', strtotime($get['start_time'])];
            }

            // 支持前端传递的keyword参数，兼容原有的name参数
            $keyword = $get['keyword'] ?? $get['name'] ?? '';
            if (!empty($keyword)) {
                $where2[] = ['nickname|real_name', 'like', '%'.trim($keyword).'%'];
                $user_id = Db::name('user')->where($where2)->column('id');
                if(empty($user_id)){
                    return ['count' => 0, 'lists' => []];
                }
                $where[] = ['user_id', 'in', $user_id];
            }

            if (!empty($get['end_time']) && $get['end_time']) {
                $where[] = ['create_time', '<=', strtotime($get['end_time'])]; // 修复这里使用end_time而不是start_time
            }

            // 提现方式筛选 - 使用withdrawal_type参数避免与status的type冲突
            if (isset($get['withdrawal_type']) && $get['withdrawal_type'] !== '') {
                $where[] = ['type', '=', $get['withdrawal_type']];
            }

            // 导出
            if (true === $is_export) {
                return self::withdrawalExport($where);
            }

            $model = new AgentWithdrawal();
            $lists = $model->field(true)
                ->with(['agent','user'])
                ->where($where)
                ->order('id desc')
                ->paginate([
                    'page'      => $get['page'],
                    'list_rows' => $get['limit'],
                    'var_page'  => 'page'
                ])->toArray();

            foreach ($lists['data'] as &$item) {
                // 处理用户头像
                if (isset($item['user']['avatar'])) {
                    $item['user']['avatar'] = empty($item['user']['avatar']) ? '' : UrlServer::getFileUrl($item['user']['avatar']);
                }

                // 添加状态描述
                $item['status_text'] = WithdrawalEnum::getStatusDesc($item['status']);

                // 为前端模板准备代理信息格式
                $item['agent_info'] = [
                    'nickname' => $item['user']['nickname'] ?? '未知代理',
                    'avatar' => $item['user']['avatar'] ?? '',
                    'agent_sn' => $item['agent']['agent_code'] ?? ''
                ];

                // 格式化时间
                $item['create_time'] = is_numeric($item['create_time']) ? date('Y-m-d H:i:s', $item['create_time']) : $item['create_time'];
                $item['examine_time'] = (isset($item['examine_time']) && is_numeric($item['examine_time']) && $item['examine_time'] > 0) ? date('Y-m-d H:i:s', $item['examine_time']) : '';
            }

            return ['count' => $lists['total'], 'lists' => $lists['data']];
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * @Notes: 提现详细
     * @Author: 张无忌
     * @param $id
     * @return array
     */
    public static function Agentdetail($id)
    {
        $withdrawal = (new AgentWithdrawal())->findOrEmpty($id)->toArray();
        $shop       = (new User())->findOrEmpty($withdrawal['user_id'])->toArray();
        $bank       = (new AgentBank())->findOrEmpty($withdrawal['bank_id'])->toArray();
        $alipay     = (new AgentAlipay())->findOrEmpty($withdrawal['alipay_id'])->toArray();

        // 格式化时间字段
        if (isset($withdrawal['create_time'])) {
            $withdrawal['create_time'] = is_numeric($withdrawal['create_time']) ?
                date('Y-m-d H:i:s', $withdrawal['create_time']) : $withdrawal['create_time'];
        }
        if (isset($withdrawal['update_time'])) {
            $withdrawal['update_time'] = is_numeric($withdrawal['update_time']) && $withdrawal['update_time'] > 0 ?
                date('Y-m-d H:i:s', $withdrawal['update_time']) : '';
        }
        if (isset($withdrawal['transfer_time'])) {
            $withdrawal['transfer_time'] = is_numeric($withdrawal['transfer_time']) && $withdrawal['transfer_time'] > 0 ?
                date('Y-m-d H:i:s', $withdrawal['transfer_time']) : '';
        }

        $withdrawal['status_text'] = WithdrawalEnum::getStatusDesc($withdrawal['status']);
        $withdrawal['type_text'] = ShopWithdrawEnum::getTypeText($withdrawal['type']);

        // 时间字段已在模型的获取器中处理，无需额外格式化

        // 确保所有字段都有默认值
        $withdrawal['explain'] = $withdrawal['explain'] ?? '';
        $withdrawal['transfer_content'] = $withdrawal['transfer_content'] ?? '';
        $withdrawal['transfer_voucher'] = $withdrawal['transfer_voucher'] ?? '';
        $withdrawal['apply_amount'] = $withdrawal['apply_amount'] ?? 0;
        $withdrawal['poundage_amount'] = $withdrawal['poundage_amount'] ?? 0;
        $withdrawal['poundage_ratio'] = $withdrawal['poundage_ratio'] ?? 0;
        $withdrawal['left_amount'] = $withdrawal['left_amount'] ?? 0;
        $withdrawal['type'] = $withdrawal['type'] ?? 0;
        $withdrawal['status'] = $withdrawal['status'] ?? 0;

        // 合并银行卡信息到 withdrawal 数组中
        if (!empty($bank)) {
            $withdrawal['bank_name'] = $bank['bank_name'] ?? '';
            $withdrawal['bank_branch'] = $bank['bank_branch'] ?? '';
            $withdrawal['bank_card'] = $bank['bank_card'] ?? '';
            $withdrawal['bank_user'] = $bank['bank_user'] ?? '';
        } else {
            $withdrawal['bank_name'] = '';
            $withdrawal['bank_branch'] = '';
            $withdrawal['bank_card'] = '';
            $withdrawal['bank_user'] = '';
        }

        // 合并支付宝信息到 withdrawal 数组中
        if (!empty($alipay)) {
            $withdrawal['alipay_account'] = $alipay['account'] ?? '';
            $withdrawal['alipay_name'] = $alipay['username'] ?? '';
        } else {
            $withdrawal['alipay_account'] = '';
            $withdrawal['alipay_name'] = '';
        }

        return [ 'withdrawal' => $withdrawal, 'user' => $shop, 'bank' => $bank, 'alipay' => $alipay ];
    }
    /**
     * @Notes: 数据汇总
     * @Author: 张无忌
     */
    public static function summary()
    {
        $model = new AgentWithdrawal();
        $successWithdrawn = floatval($model->where(['status'=>WithdrawalEnum::SUCCESS_STATUS])->sum('apply_amount'));
        $handleWithdrawn = floatval($model->where(['status'=>WithdrawalEnum::HANDLE_STATUS])->sum('apply_amount'));
        $totalWallet = floatval(Db::name('agent_settlement')->sum('cn_price'));
        $totalWallet = $totalWallet - $handleWithdrawn - $successWithdrawn;
        return ['successWithdrawn'=>$successWithdrawn, 'handleWithdrawn'=>$handleWithdrawn, 'totalWallet'=>$totalWallet];
    }
    /**
     * @Notes: 统计
     * @Author: 张无忌
     * @return array
     */
    public static function statistics2()
    {
        $model = new AgentWithdrawal();
        $apply   = $model->where(['status'=> 0])->count(); // WithdrawalEnum::APPLY_STATUS
        $handle  = $model->where(['status'=> 1])->count(); // WithdrawalEnum::HANDLE_STATUS
        $success = $model->where(['status'=> 2])->count(); // WithdrawalEnum::SUCCESS_STATUS
        $error   = $model->where(['status'=> 3])->count(); // WithdrawalEnum::ERROR_STATUS

        return ['apply'=>$apply, 'handle'=>$handle, 'success'=>$success, 'error'=>$error];
    }


    /**
     * @Notes: 结算统计
     * @return array
     */
    public static function statistics($shop_id = 0)
    {
        $settleOrederAmount = floatval(AgentOrder::where(['order_type' => 2, 'status' => 2])->sum('money')); //已结算商家入驻费总额
        $settlejyOrederAmount = floatval(AgentOrder::where(['order_type' => 3, 'status' => 2])->sum('money')); //已结算商家检验费总额
        $settleOrederAmountWait = floatval(AgentOrder::where(['order_type' => 2, 'status' => 1])->sum('money')); //待结算商家入驻费总额
        $settlejyOrederAmountWait = floatval(AgentOrder::where(['order_type' => 3, 'status' => 1])->sum('money')); //待结算商家检验费总额
        $settleWithdrawalAmount = floatval(AgentOrder::where(['order_type' => 1, 'status' => 2])->sum('money')); //已结算会员购买佣金
        $settleWithdrawalAmount3 = floatval(AgentOrder::where(['order_type' => 4, 'status' => 2])->sum('money')); //已结算会员购买佣金
        $settleWithdrawalAmountwait = floatval(AgentOrder::where(['order_type' => 1, 'status' => 1])->sum('money')); //待结算会员购买佣金
        $settleWithdrawalAmountwait3 = floatval(AgentOrder::where(['order_type' => 4, 'status' => 1])->sum('money')); //待结算会员购买佣金

        return [
            'settleOrederAmount' => $settleOrederAmount, //已结算商家入驻费总额
            'settlejyOrederAmount' => $settlejyOrederAmount, //已结算商家检验费总额
            'settleOrederAmountWait' => $settleOrederAmountWait, //待结算商家入驻费总额
            'settlejyOrederAmountWait' => $settlejyOrederAmountWait, //待结算商家检验费总额
            'settleWithdrawalAmount' => $settleWithdrawalAmount, //已结算会员购买佣金
            'settleWithdrawalAmountwait' => $settleWithdrawalAmountwait, //待结算会员购买佣金
            'settleWithdrawalAmount3' => $settleWithdrawalAmount3, //待结算会员购买佣金
            'settleWithdrawalAmountwait3' => $settleWithdrawalAmountwait3, //待结算会员购买佣金
            // 添加佣金明细页面需要的统计数据
            'total_commission' => $settleWithdrawalAmount + $settleWithdrawalAmountwait + $settleWithdrawalAmount3 + $settleWithdrawalAmountwait3,
            'settled_commission' => $settleWithdrawalAmount + $settleWithdrawalAmount3,
            'pending_commission' => $settleWithdrawalAmountwait + $settleWithdrawalAmountwait3,
            'agent_count' => AgentOrder::distinct()->count('user_id')
        ];
    }


    /*
     * 代理结算
     */
//    public static function settle($get)
//    {
//        try {
//            $where = [];
//
//            // 添加其他查询条件（如果有的话）
//            if (!empty($get['start_time'])) {
//                $where[] = ['create_time', '>=', strtotime($get['start_time'])];
//            }
//
//            if (!empty($get['end_time'])) {
//                $where[] = ['create_time', '<=', strtotime($get['end_time'])];
//            }
//
//            // 计算每种 order_type 的待返佣和已结算费用之和
//            $result = Db::name('agent_order')
//                ->field([
//                    'order_type',
//                    Db::raw('SUM(CASE WHEN status = 1 THEN money ELSE 0 END) AS pending_commissions'),
//                    Db::raw('SUM(CASE WHEN status = 2 THEN money ELSE 0 END) AS settled_commissions'),
//                ])
//                ->where($where)
//                ->group('order_type')
//                ->select()
//                ->toArray();
//            $order_type=[1=>'用户集采购会员',2=>'商家入驻费',3=>'商家检验费',4=>'入驻及检验费组合'];
//            foreach ($result as &$item) {
//                $item['order_type']=$order_type[$item['order_type']];
//            }
//            return ['data' => $result];
//        } catch (\Exception $e) {
//            return ['error' => $e->getMessage()];
//        }
//    }

    /*
    * 代理结算
    */
    public static function settle($get)
    {
        try {
            $where = [];

            // 代理信息搜索
            if (isset($get['keyword']) && !empty($get['keyword'])) {
                $userIds = User::where('sn|nickname', 'like', '%' . $get['keyword'] . '%')
                    ->column('id');
                if (!empty($userIds)) {
                    $where[] = ['a.user_id', 'in', $userIds];
                } else {
                    // 如果没有找到匹配的用户，返回空结果
                    return [
                        'count' => 0,
                        'lists' => []
                    ];
                }
            }

            // 结算状态筛选
            if (isset($get['status']) && $get['status'] !== '') {
                $where[] = ['a.status', '=', $get['status']];
            }

            // 时间范围筛选
            if (!empty($get['start_time'])) {
                $where[] = ['a.create_time', '>=', strtotime($get['start_time'])];
            }

            if (!empty($get['end_time'])) {
                $where[] = ['a.create_time', '<=', strtotime($get['end_time'])];
            }

            // 计算每种 order_type 的待返佣和已结算费用之和
            //`money` decimal(10,2) unsigned NOT NULL COMMENT '佣金',
            //`status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-待返佣；2-已结算；3-失效；',
            //计算每个用户的待返佣的总金额,已结算的总金额,失效的总金额
            $lists = Db::name('agent_order')->alias('a')
                ->field('a.*,u.nickname,u.mobile,u.avatar,u.sn,sum(case when a.status=1 then money else 0 end) as pending_commissions,sum(case when a.status=2 then money else 0 end) as settled_commissions,sum(case when a.status=3 then money else 0 end) as invalid_commissions')
                ->leftJoin('user u','u.id=a.user_id')
                ->where($where)
                ->group('a.user_id')
                ->paginate([
                    'page'      => $get['page'] ?? 1,
                    'list_rows' => $get['limit'] ?? 10,
                    'var_page'  => 'page'
                ])->toArray();

            foreach ($lists['data'] as &$item) {
                $item['avatar'] = empty($item['avatar']) ? '' : UrlServer::getFileUrl($item['avatar']);

                // 为前端模板准备代理信息格式
                $item['agent_info'] = [
                    'nickname' => $item['nickname'] ?? '未知代理',
                    'avatar' => $item['avatar'],
                    'agent_sn' => $item['sn'] ?? ''
                ];

                // 格式化时间
                $item['create_time'] = is_numeric($item['create_time']) ? date('Y-m-d H:i:s', $item['create_time']) : $item['create_time'];
                $item['settlement_period'] = '月结'; // 可以根据实际业务调整
                $item['commission_amount'] = floatval($item['settled_commissions']) + floatval($item['pending_commissions']);
                $item['order_count'] = 1; // 这里可以根据实际需求计算订单数量
                $item['settlement_time'] = $item['status'] == 2 ? $item['create_time'] : '';
            }

            return ['count' => $lists['total'], 'lists' => $lists['data']];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }



    /**
     * @notes 用户列表
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/9/3 9:55
     */
    public static function getUserLists($params)
    {
        $where[] = ['del', '=', 0];
        $where[] = ['user_delete', '=', 0];
        $where[] = ['is_agent', '=', 0];
        // 用户信息
        if (isset($params['keyword']) && !empty($params['keyword'])) {
            $where[] = ['sn|nickname|mobile', 'like', '%' . $params['keyword'] . '%'];
        }
        $lists = User::field('id,sn,nickname,id as distribution,mobile')
            ->where($where)
            ->page($params['page'], $params['limit'])
            ->select()
            ->toArray();
        $count = User::where($where)->count();

        return [
            'count' => $count,
            'lists' => $lists,
        ];
    }

    /**
     * @notes 开通分销会员
     * @param $params
     * @return bool
     * <AUTHOR>
     * @date 2021/9/3 11:09
     */
    public static function open($params)
    {
        try {
            Db::startTrans();
            $user = User::where('id', $params['user_id'])->findOrEmpty()->toArray();
            if (empty($user)) {
                throw new \Exception('用户不存在');
            }
            if (User::UserIsDelete($params['user_id'])) {
                throw new \Exception('用户已注销');
            }
            $distribution = Agent::where('user_id', $params['user_id'])->findOrEmpty()->toArray();
            if (!empty($distribution) && $distribution['is_agent'] == 1) {
                throw new \Exception('用户已是代理了');
            }
            $params['distribution_start_time'] = strtotime($params['distribution_start_time']);
            $params['distribution_end_time'] = strtotime($params['distribution_end_time']);
            #增加时间校验,end_time不能为空,start_time不能大于end_time.也不能为空
            if ($params['distribution_start_time'] > $params['distribution_end_time']) {
                throw new \Exception('开始时间不能大于结束时间');
            }

            if (!empty($distribution) && $distribution['is_freeze'] == 1) {
                throw new \Exception('用户已冻结,请联系管理员');
            }
            #生成8位英文加数字随机码
            $agent_code = self::getRandomCode(8);
            $pid=self::getAgentSupervisor($params['user_id']);
            if (empty($distribution)) {
                $data = [
                    'user_id' => $params['user_id'],
                    'level' => 1,
                    'sponsor_id' => $pid,
                    'distribution_start_time' => $params['distribution_start_time'],
                    'distribution_end_time' => $params['distribution_end_time'],
                    'is_freeze' => 0,
                    'remark' => '后台开通代理',
                    'agent_code' => $agent_code,
                    'create_time' => time(),
                    'buy_type' => '系统设置',
                ];

                Agent::create($data);
                $id = Agent::where('user_id', $params['user_id'])->value('id');
                self::upgradeToAgent($params['user_id'],$id);
                
                // 检查并记录保证金金额
                $depositAmount = self::getUserDepositAmount($params['user_id']);
                if ($depositAmount > 0) {
                    // 记录保证金信息
                    $depositData = [
                        'user_id' => $params['user_id'],
                        'amount' => $depositAmount,
                        'status' => 0, // 待支付
                        'create_time' => time(),
                        'remark' => '系统设置代理保证金'
                    ];
                    Db::name('agent_merchantfees')->insert($depositData);
                }
            }
            Db::commit();
            return true;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            Db::rollback();
            return false;
        }
    }



    public static function getRandomCode($length = 8)
    {
        $str = null;
        $strPol = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz";
        $max = strlen($strPol) - 1;
        for ($i = 0; $i < $length; $i++) {
            $str .= $strPol[rand(0, $max)];//rand($min,$max)生成介于min和max两个数之间的一个随机整数
        }
        return $str;
    }

    public static function getUser($params)
    {
        $field = [
            'u.id' => 'user_id',
            'u.sn' => 'user_sn',
            'u.nickname' => 'user_nickname',
            'd.distribution_start_time' => 'distribution_start_time',
            'd.distribution_end_time' => 'distribution_end_time'
        ];
        $info = Agent::alias('d')
            ->leftJoin('user u', 'u.id = d.user_id')
            ->field($field)
            ->where('d.user_id', $params['id'])
            ->findOrEmpty()
            ->toArray();
        if (isset($info['distribution_start_time'])) {
            $info['distribution_start_time'] = date('Y-m-d', $info['distribution_start_time']);
            $info['distribution_end_time'] = date('Y-m-d', $info['distribution_end_time']);
        }
        return $info;
    }

    /**
     * @notes 分销会员等级调整
     * @param $params
     * @return bool
     * <AUTHOR>
     * @date 2021/9/3 14:14
     */
    public static function adjust($params)
    {
        try {
            if (User::UserIsDelete($params['user_id'])) {
                throw new \Exception('用户已注销');
            }
            Agent::where(['user_id' => $params['user_id']])->update([
                'distribution_start_time' => strtotime($params['distribution_start_time']),
                'distribution_end_time' => strtotime($params['distribution_end_time']),
            ]);
            return true;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 冻结资格/恢复资格
     * @param $params
     * @return bool
     * <AUTHOR>
     * @date 2021/9/3 14:24
     */
    public static function isFreeze($params)
    {
        try {
            if (User::UserIsDelete($params['user_id'])) {
                throw new \Exception('用户已注销');
            }
            Agent::where(['user_id' => $params['user_id']])->update([
                'is_freeze' => $params['is_freeze']
            ]);

            return true;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @notes 代理提现导出
     * @param array $params 查询参数
     * @return array
     * <AUTHOR>
     * @date 2021/7/28 11:50
     */
    public static function withdrawalExport($params)
    {
        try {
            $limit = $params['limit'] ?? 10000;
            $page = $params['page'] ?? 1;
            $where = [];

            if (!empty($params['agent_id'])) {
                $where[] = ['aw.agent_id', '=', $params['agent_id']];
            }
            if (!empty($params['user_info'])) {
                $userInfo = $params['user_info'];
                $where[] = ['u.nickname|u.mobile|u.sn', 'like', '%' . $userInfo . '%'];
            }
            if (!empty($params['sn'])) {
                $where[] = ['aw.sn', '=', $params['sn']];
            }
            if (isset($params['status']) && $params['status'] !== '') {
                $where[] = ['aw.status', '=', $params['status']];
            }
            if (!empty($params['type'])) {
                $where[] = ['aw.type', '=', $params['type']];
            }
            if (!empty($params['date_range'])) {
                $range = explode('~', $params['date_range']);
                $startTime = strtotime(trim($range[0]));
                $endTime = strtotime(trim($range[1])) + 86399;
                $where[] = ['aw.create_time', 'between', [$startTime, $endTime]];
            }

            $lists = AgentWithdrawal::alias('aw')
                ->leftJoin('user u', 'u.id = aw.user_id')
                ->field('aw.sn, u.nickname, u.mobile, aw.money, aw.type, aw.status, aw.create_time, aw.handle_time, aw.handle_remark') // Select fields for export
                ->where($where)
                ->order('aw.id', 'desc')
                ->page($page, $limit)
                ->select()
                ->toArray();

            // Format data if needed
            foreach ($lists as &$item) {
                $item['status_desc'] = WithdrawalEnum::getStatusDesc($item['status']);
                $item['type_desc'] = WithdrawEnum::getTypeDesc($item['type'] ?? 0);
                $item['create_time_str'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['handle_time_str'] = $item['handle_time'] ? date('Y-m-d H:i:s', $item['handle_time']) : '-';
                // 保留原始字段，以便导出时使用
            }

            return ['count' => count($lists), 'lists' => $lists];
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return ['count' => 0, 'lists' => [], 'error' => $e->getMessage()];
        }
    }

    public static function setConfig($post)
    {
        // Validate input
        // 代理保证金金额
        if (!isset($post['agent_deposit_amount']) || !is_numeric($post['agent_deposit_amount']) || $post['agent_deposit_amount'] < 0.01) {
            return JsonServer::error('代理保证金金额必须是大于等于0.01的数字');
        }
        // 保证金公示期天数
        if (!isset($post['deposit_publicity_period_days']) || !is_numeric($post['deposit_publicity_period_days']) || $post['deposit_publicity_period_days'] < 0) {
            return JsonServer::error('保证金公示期天数必须是大于等于0的整数');
        }

        // 新增：申请后公示期天数验证
        if (!isset($post['refund_publicity_period_days']) || !is_numeric($post['refund_publicity_period_days']) || $post['refund_publicity_period_days'] < 0) {
            return JsonServer::error('申请后公示期天数必须是大于等于0的整数');
        }

        $post['deposit_publicity_period_days'] = intval($post['deposit_publicity_period_days']); // Ensure it's an integer
        $post['refund_publicity_period_days'] = intval($post['refund_publicity_period_days']); // Ensure it's an integer

        try {
            // 开始事务
            \think\facade\Db::startTrans();

            // 保存配置到agent_setting配置组
            ConfigServer::set('agent_setting', '', $post);

            // 同时保存到agent配置组
            foreach ($post as $key => $value) {
                ConfigServer::set('agent', $key, $value);
            }

            // 提交事务
            \think\facade\Db::commit();

            // 手动清除缓存
            \think\facade\Cache::delete('config-agent_setting--0');
            \think\facade\Cache::delete('config-agent_setting-null-0');

            // 清除agent配置组的缓存
            foreach ($post as $key => $value) {
                \think\facade\Cache::delete('config-agent-' . $key . '-0');
            }

            // 记录日志，方便调试
            \think\facade\Log::info('保存代理配置：' . json_encode($post));

            return JsonServer::success('保存成功');
        } catch (\Exception $e) {
            // 回滚事务
            \think\facade\Db::rollback();

            // 记录错误日志
            \think\facade\Log::error('保存代理配置失败：' . $e->getMessage());

            return JsonServer::error('保存失败：' . $e->getMessage());
        }
    }


    /**
     * @notes 获取代理配置
     * @return array
     * <AUTHOR>
     * @date 2022/4/28 16:13
     */
    public static function getConfig()
    {
        // 手动清除缓存，确保获取最新配置
        \think\facade\Cache::delete('config-agent_setting--0');
        \think\facade\Cache::delete('config-agent_setting-null-0');

        // 清除agent配置组的缓存
        $keys = [
            'area_xz', 'first_ratio', 'two_ratio', 'pt_ratio', 'js_date',
            'agent_price', 'agnet_bg_image', 'withdrawal_fee', 'ktx_explain',
            'djs_explain', 'ydj_explain', 'agent_deposit_amount',
            'deposit_publicity_period_days', 'refund_publicity_period_days',
            'agent_deposit_refund_warning_image',
            // 新增配置项
            'purchase_direct_ratio', 'purchase_indirect_ratio',
            'merchant_direct_ratio', 'merchant_indirect_ratio',
            'inspection_direct_ratio', 'inspection_indirect_ratio'
        ];

        foreach ($keys as $key) {
            \think\facade\Cache::delete('config-agent-' . $key . '-0');
        }

        // 额外清除新配置项的缓存
        \think\facade\Cache::delete('config-agent-agent_deposit_refund_warning_image-0');

        // 从agent配置组中获取配置
        $config = [
            'area_xz' => ConfigServer::get('agent', 'area_xz', 0),
            'first_ratio' => ConfigServer::get('agent', 'first_ratio', 0),
            'two_ratio' => ConfigServer::get('agent', 'two_ratio', 0),
            'pt_ratio' => ConfigServer::get('agent', 'pt_ratio', 0),
            'js_date' => ConfigServer::get('agent', 'js_date', 1),
            'agent_price' => ConfigServer::get('agent', 'agent_price', 0.01),
            'agnet_bg_image' => ConfigServer::get('agent', 'agnet_bg_image', 0.01),
            'withdrawal_fee' => ConfigServer::get('agent', 'withdrawal_fee', 0.01),
            'ktx_explain' => ConfigServer::get('agent', 'ktx_explain', 0.01),
            'djs_explain' => ConfigServer::get('agent', 'djs_explain', 0.01),
            'ydj_explain' => ConfigServer::get('agent', 'ydj_explain', 0.01),
            'agent_deposit_amount' => ConfigServer::get('agent', 'agent_deposit_amount', 0.01),
            'deposit_publicity_period_days' => ConfigServer::get('agent', 'deposit_publicity_period_days', 0),
            'refund_publicity_period_days' => ConfigServer::get('agent', 'refund_publicity_period_days', 90),
            'agent_deposit_refund_warning_image' => ConfigServer::get('agent', 'agent_deposit_refund_warning_image', ''),
            // 新增配置项
            'purchase_direct_ratio' => ConfigServer::get('agent', 'purchase_direct_ratio', 0),
            'purchase_indirect_ratio' => ConfigServer::get('agent', 'purchase_indirect_ratio', 0),
            'merchant_direct_ratio' => ConfigServer::get('agent', 'merchant_direct_ratio', 0),
            'merchant_indirect_ratio' => ConfigServer::get('agent', 'merchant_indirect_ratio', 0),
            'inspection_direct_ratio' => ConfigServer::get('agent', 'inspection_direct_ratio', 0),
            'inspection_indirect_ratio' => ConfigServer::get('agent', 'inspection_indirect_ratio', 0),
        ];

        // 记录最终配置
        \think\facade\Log::info('最终代理配置：' . json_encode($config));

        return $config;
    }
    
    /**
     * @notes 获取用户的保证金金额
     * @param int $userId 用户ID
     * @return float 保证金金额
     * <AUTHOR> @date
     */
    public static function getUserDepositAmount($userId)
    {
        // 查询用户的自定义保证金金额
        $user = User::where('id', $userId)->find();
        if ($user && !is_null($user->custom_deposit_amount) && $user->custom_deposit_amount > 0) {
            return $user->custom_deposit_amount;
        }
        
        // 如果没有设置自定义金额，使用通用配置
        return ConfigServer::get('agent', 'agent_deposit_amount', 0.01);
    }

    /**
    * @notes  冻结代理
    * @return array
    * <AUTHOR>
    * @date 2022/4/28 16:13
    */
    public static function freeze($params)
    {

        $data['is_freeze']=$params['is_freeze'];
        if($params['is_freeze']){
            $data['freeze_time']=time();
            Db::name('agent_order')->where(['user_id'=>$params['user_id'],'status'=>1])->update(['status'=>3,'remark'=>'冻结代理自动冻结未结算佣金']);
        }else{
            $data['open_time']=time();
            Db::name('agent_order')->where(['user_id'=>$params['user_id'],'status'=>3,'remark'=>'冻结代理自动冻结未结算佣金'])->update(['status'=>1]);
        }
        $agent_id=Db::name('agent')->where(['user_id'=>$params['user_id']])->update($data);
        return $agent_id;
    }

    /*
     * @notes
     */
    public static function thaw($params)
    {
        $data['is_freeze']=0;
        $agent_id=Db::name('agent')->where(['user_id'=>$params['user_id']])->update($data);
        return $agent_id;

    }

    /**
     * 获取除给定用户外，他所有的非代理下级用户ID
     *
     * @param int $userId 给定用户的ID
     * @return array 包含所有非代理下级用户ID的数组
     */
    public static function getNonAgentSubordinates($userId)
    {
        $result = [];

        // 递归函数
        $findSubordinates = function ($parentId) use (&$result, &$findSubordinates) {
            // 查询所有直接下级
            $subordinates = AgentRelationship::where('sponsor_id', $parentId)->select()->toArray();

            foreach ($subordinates as $subordinate) {
                $subordinateId = $subordinate['user_id'];

                // 检查该用户是否为代理
                if (!Agent::where('user_id', $subordinateId)->find()) {
                    // 如果不是代理，则添加到结果数组中
                    $result[] = $subordinateId;

                    // 递归查找该用户的下级
                    $findSubordinates($subordinateId);
                }
            }
        };

        // 从给定用户开始查找下级
        $findSubordinates($userId);

        // 排除给定的用户ID
        $result = array_diff($result, [$userId]);

        return $result;
    }


    // 添加邀请关系
    public static function addInviteRelation($inviterId, $inviteeId)
    {
        AgentRelationship::create([
            'user_id' => $inviteeId,
            'sponsor_id' => $inviterId,
            'agent_id' => self::getEffectiveAgentId($inviterId) // 获取当前有效的代理ID
        ]);
    }

    // 获取当前有效的代理ID
    private static function getEffectiveAgentId($userId)
    {
        $user = User::find($userId);
        if ($user && $user->is_agent) {
            return $user->agent_id;
        }
        return null;
    }

    // 用户升级为代理
    public static function upgradeToAgent($userId, $newAgentId)
    {
        // 更新用户表
        User::where('id', $userId)->update(['is_agent' => 1, 'agent_id' => $newAgentId]);

        // 更新关系绑定表中的agent_id
        self::updateSubordinatesAgentId($userId, $newAgentId);
    }

    // 更新下级用户的agent_id
    private static function updateSubordinatesAgentId($userId, $newAgentId)
    {
        // 获取所有符合条件的下级用户ID
        $subordinateIds = self::getAllSubordinatesAgentsAndNonAgentDescendants($userId);
        // 更新关系绑定表中的agent_id
        if(!empty($subordinateIds['all_user'])){
            AgentRelationship::whereIn('user_id', $subordinateIds['all_user'])->update(['agent_id' => $newAgentId]);
        }
    }



    // 获取指定用户的直属下级、直属代理及其下级的非代理用户ID，并组合成all_user
    public static function getAllSubordinatesAgentsAndNonAgentDescendants($userId)
    {
        $result = [
            'direct_subordinates' => [],
            'direct_agents' => [],//直属代理
            'non_agent_descendants_of_agents' => []
        ];
        self::processUser($userId, $result);
        // 组合成all_user
        $result['all_user'] = array_merge($result['direct_subordinates'], $result['non_agent_descendants_of_agents']);

        return $result;
    }

    // 递归函数，用于处理指定用户及其下级
    private static function processUser($userId, &$result)
    {
        // 查询当前用户的所有直属下级和直属代理用户ID
        $relationships = Db::name('agent_relationships')
            ->where('sponsor_id', $userId)
            ->select();

        foreach ($relationships as $relationship) {
            $subordinateId = $relationship['user_id'];
            $subordinate = User::find($subordinateId);

            if (!$subordinate) {
                // 如果用户不存在，记录错误日志（可选）
                error_log("User with ID {$subordinateId} not found.");
                continue;
            }

            if ($subordinate->is_agent) {
                // 如果下级是代理，则添加到'direct_agents'数组
                $result['direct_agents'][] = $subordinateId;

                // 获取该代理的所有非代理下级用户ID
                $agentDescendants = self::getAllNonAgentDescendants($subordinateId);
                $result['non_agent_descendants_of_agents'] = array_merge($result['non_agent_descendants_of_agents'], $agentDescendants);
            } else {
                // 如果下级不是代理，则添加到'direct_subordinates'数组，并递归处理其下级
                $result['direct_subordinates'][] = $subordinateId;
                self::processNonAgentDescendants($subordinateId, $result['non_agent_descendants_of_agents']);
            }
        }
    }



    // 递归函数，用于获取指定非代理用户的所有非代理下级用户ID
    private static function processNonAgentDescendants($userId, &$result)
    {
        // 查询当前用户的所有直接下级用户ID
        $subordinates = Db::name('agent_relationships')
            ->where('sponsor_id', $userId)
            ->column('user_id');

        foreach ($subordinates as $subordinateId) {
            $subordinate = User::find($subordinateId);

            if (!$subordinate) {
                // 如果用户不存在，记录错误日志（可选）
                error_log("User with ID {$subordinateId} not found.");
                continue;
            }

            if (!$subordinate->is_agent) {
                // 将非代理下级用户ID添加到结果数组中
                $result[] = $subordinateId;

                // 递归获取该非代理用户的所有非代理下级用户ID
                self::processNonAgentDescendants($subordinateId, $result);
            }
        }
    }



    // 获取指定代理用户的所有非代理下级用户ID
    private static function getAllNonAgentDescendants($agentUserId)
    {
        $result = [];
        self::processNonAgentDescendants($agentUserId, $result);
        return $result;
    }
    /**
     * @notes 撤销代理
     * @param array $params ['agent_id' => int, 'revoke_reason' => string, 'transfer_to_agent_id' => int|null, 'admin_id' => int]
     * @return bool
     * @throws \Exception
     */
    public static function revokeAgent($params)
    {
        $agentId = $params['agent_id'];
        $agent = Agent::find($agentId);
        if (!$agent) {
            throw new \Exception('代理不存在');
        }

        // 创建撤销记录
        AgentRevokeRecords::create([
            'user_id' => $agent->user_id,
            'agent_id' => $agentId,
            'revoke_reason' => $params['revoke_reason'] ?? '后台操作撤销',
            'revoke_time' => time(), // 使用 time() 函数
            'operator_id' => $params['admin_id'] ?? 0, // 假设有操作员ID
        ]);

        // 更新用户表中的代理状态
        User::where('id', $agent->user_id)->update(['is_agent' => 0]);
        // 删除或标记代理记录为无效
        $agent->delete(); // 或者 $agent->save(['status' => 'revoked']);

        // 获取所有直接下级代理并转移
        $subordinateAgents = Agent::where('sponsor_id', $agentId)->select();
        $newSponsorId = $params['transfer_to_agent_id'] ?? 0; // 0 代表平台

        // 转移下级代理并记录
        foreach ($subordinateAgents as $subAgent) {
            $subordinateId = $subAgent->user_id;
            UserTransferRecords::create([
                'transferred_user_id' => $subordinateId,
                'original_agent_id' => $agentId,
                'new_agent_id' => $newSponsorId, // 转移给平台(0)，或指定新代理ID
                'transfer_time' => time(), // 使用 time() 函数
                'transfer_reason' => '上级代理被撤销',
                'operator_id' => $params['admin_id'] ?? 0
            ]);

            // 更新下级代理的上级ID
            $subAgent->sponsor_id = $newSponsorId;
            $subAgent->save();

            // 注意：AgentRelationship 表的逻辑可能需要根据实际情况调整，这里仅更新 Agent 表的 sponsor_id
        }
    }



    // 获取所有下级用户ID（包括间接下级）
    private static function getAllSubordinates($agentId)
    {
        $subordinates = [];
        $queue = [$agentId];

        while (!empty($queue)) {
            $currentUserId = array_shift($queue);

            // 获取当前用户的所有下级（包括代理和非代理）
            $directSubordinates = AgentRelationship::where('sponsor_id', $currentUserId)
                ->orWhere('agent_id', $currentUserId) // 如果agent_id是当前代理的ID，则也视为下级
                ->select()->column('user_id');

            $subordinates = array_merge($subordinates, $directSubordinates);

            // 将非代理下级加入队列继续查找
            foreach ($directSubordinates as $subordinateId) {
                $subordinate = User::find($subordinateId);
                if (!$subordinate || $subordinate->is_agent) {
                    continue;
                }
                $queue[] = $subordinateId;
            }
        }

        return array_unique($subordinates);
    }
    public static  function insertRandomUsers($count)
    {
        $users = [];

        for ($i = 0; $i < $count; $i++) {
            $user = [
                'sn' => Str::random(32),
                'root' => rand(0, 1),
                'nickname' => Str::random(10),
                'avatar' => 'uploads/images/20241210/202412101031303dcf87611.png',
                'mobile' => '1' . Str::random(10),
                'real_name' => Str::random(5) . ' ' . Str::random(5),
                'level' => rand(0, 3),
                'group_id' => rand(1, 10),
                'sex' => rand(0, 2),
                'birthday' => strtotime('now - ' . rand(0, 80) . ' years'),
                'user_money' => sprintf('%.2f', rand(0, 1000) / 100),
                'user_integral' => rand(0, 10000),
                'total_order_amount' => sprintf('%.2f', rand(0, 10000) / 100),
                'total_recharge_amount' => sprintf('%.2f', rand(0, 10000) / 100),
                'account' => Str::random(8),
                'password' => password_hash(Str::random(8), PASSWORD_DEFAULT),
                'pay_password' => password_hash(Str::random(8), PASSWORD_DEFAULT),
                'salt' => Str::random(4),
                'first_leader' => rand(0, 100),
                'second_leader' => rand(0, 100),
                'third_leader' => rand(0, 100),
                'ancestor_relation' => json_encode(['leader1' => rand(0, 100), 'leader2' => rand(0, 100), 'leader3' => rand(0, 100)]),
                'is_distribution' => rand(0, 1),
                'distribution_add_remarks' => Str::random(30),
                'freeze_distribution' => rand(0, 1),
                'distribution_h5_qr_code' => Str::random(32),
                'distribution_mnp_qr_code' => Str::random(32),
                'distribution_app_qr_code' => Str::random(32),
                'distribution_code' => Str::random(12),
                'create_time' => time(),
                'update_time' => time(),
                'login_time' => time(),
                'login_ip' => long2ip(rand(1, 0xFFFFFFFF)),
                'disable' => rand(0, 1),
                'del' => 0,
                'user_growth' => rand(0, 1000),
                'earnings' => sprintf('%.2f', rand(0, 1000) / 100),
                'client' => rand(0, 2),
                'tag_ids' => implode(',', range(1, rand(1, 5))),
                'remark' => Str::random(50),
                'is_new_user' => rand(0, 1),
                'user_delete' => rand(0, 1),
                'shop_id' => 0,
                'jcvip' => 0,
                'vip_time' =>0,
                'is_agent' => 0,
                'agent_id' => 0,
            ];
            $users[] = $user;
        }

        Db::name('user')->insertAll($users);

        return 'Successfully inserted ' . $count . ' random users.';
    }


    // 获取指定用户的上级，直到找到一个代理上级或到达层级顶部
    public static function getAgentSupervisor($userId)
    {
        $id = self::findAgentSupervisor($userId);
        if ($id === $userId) {
            return 0;
        }

        return $id ?? 0;
    }

    // 递归函数，用于查找指定用户的代理上级
    private static function findAgentSupervisor($userId)
    {


        // 查询当前用户的直接上级
        $supervisorId = Db::name('agent_relationships')
            ->where('user_id', $userId)
            ->value('sponsor_id');
        // 如果没有找到上级，则返回null
        if (!$supervisorId) {
            return null;
        }
        // 加载用户信息
        $user = User::find($supervisorId);

        if (!$user) {
            // 如果用户不存在，可以选择抛出异常或返回null
            return null;
        }

        // 如果当前用户是代理，则返回其user_id
        if ($user->is_agent) {
            return $user->id;
        }




        // 递归查找上级的代理上级
        return self::findAgentSupervisor($supervisorId);
    }

    //佣金提现列表

    public static function cashList($get)
    {
        $get['limit']=$get['limit']??10;
        $where= [];
        //时间区间
        if(isset($get['month']) && !empty($get['month'])){
            $start_time = strtotime($get['month'] . '-01 00:00:00');
            $end_time = strtotime('+1 month', $start_time);
            $where[]=['a.create_time','between',[$start_time,$end_time]];
        }
        $lists = AgentWithdrawal::alias('a')
            ->where(['a.user_id' => $get['agent_id']])
            ->where($where)
            ->order('a.create_time', 'desc')
            ->paginate([
                'page' => $get['page_no'],
                'list_rows' => $get['limit'],
                'var_page' => 'page'
            ])
            ->toArray();
        foreach ($lists['data'] as $k => &$v) {
            if ($v['status'] == 1) {
                $v['status_txt'] = '待审核';
            } else if ($v['status'] == 2) {
                $v['status_txt'] = '已提现';
            } else if ($v['status'] == 3) {
                $v['status_txt'] = '审核拒绝';
            }
        }
        return ['count' => $lists['total'], 'lists' => $lists['data']];
    }

    /**
     * @Notes: 商家结算记录
     * @Author: 张无忌
     * @param $get
     * @return array
     */
    public static function record($get)
    {
        $get['limit']=$get['limit']??10;
        //时间区间
        $where= [];
        if(isset($get['month']) && !empty($get['month'])){
            $start_time = strtotime($get['month'].'-01 00:00:00');
            $end_time = strtotime('+1 month', $start_time);
            $where[]=['a.create_time','between',[$start_time,$end_time]];
        }
        try {
            $lists = AgentOrder::alias('a')
                ->field('a.id as aid,b.*,a.*')
                ->Leftjoin('agent_settlement b','a.id=b.agent_order_id')
                ->where(['a.user_id'=>$get['agent_id']])
                ->where($where)
                ->order('a.user_id', 'desc')
                ->paginate([
                    'page'      => $get['page'],
                    'list_rows' => $get['limit'],
                    'var_page' => 'page'
                ])
                ->toArray();
            $js_date=ConfigServer::get('agent', 'js_date', 0);
            $status_txt=[1=>'待结算',2=>'已结算',3=>'冻结',4=>'订单失效'];
            $order_type=[1=>'用户集采购会员',2=>'商家入驻费',3=>'商家检验费',4=>'入驻及检验费组合'];
            foreach($lists['data'] as $k=>&$v){
                $v['yu_settlement_time']=date('Y-m-d H:i:s',strtotime($v['create_time'])+$js_date*86400);
                $v['status_txt']=$status_txt[$v['status']];
                if($v['status']==3){
                    $v['freeze_status']=0;
                }
                if($v['status']==1){
                    $v['freeze_status']=1;//只有未结算才能冻结
                }
                $v['order_type']=$order_type[$v['order_type']];
                $v['ratio']= ($v['ratio']*100).'%';
                $v['settlement_time']= $v['settlement_time']??'未结算';
            }


            return ['count'=>$lists['total'], 'lists'=>$lists['data']];
        } catch (\Exception $e) {
            return ['error'=>$e->getMessage()];
        }
    }


    /**
     * @notes 审核佣金
     * @param $post
     * @return bool
     * <AUTHOR>
     * @date 2022/5/12 16:57
     */
    public static function audit($post)
    {
        Db::startTrans();
        try {
            $article = AgentOrder::findOrEmpty($post['id']);
            if (!$article) {
                throw new \Exception('佣金不存在');
            }

            switch ($article['status']) {
                case 2:
                    throw new \Exception('佣金已结算,不能操作');
                case 4:
                    throw new \Exception('佣金已失效,不能操作');
                case 3:
                    if ($post['status'] == 1) {
                        throw new \Exception('佣金已冻结,不能操作');
                    }
                    break;
                case 0:
                    if ($post['status'] == 0) {
                        throw new \Exception('佣金正常,修改无效');
                    }
                    break;
            }

            $article->status = $post['status'] ? 3 : 1;
            $article->remark = $post['remark'] ?? '';
            $article->save();
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @Notes: 结算详细
     * @Author: 张无忌
     * @param $get
     * @return array
     */
    public static function detail($get)
    {
        try {
            $where[] = ['id', '=', (int)$get['id']];

            $order_info= AgentOrder::field(true)
                ->where($where)
                ->find()
                ->toArray();

            // 格式化时间字段
            if (isset($order_info['create_time'])) {
                $order_info['create_time'] = is_numeric($order_info['create_time']) ?
                    date('Y-m-d H:i:s', $order_info['create_time']) : $order_info['create_time'];
            }
            if (isset($order_info['settlement_time'])) {
                $order_info['settlement_time'] = is_numeric($order_info['settlement_time']) && $order_info['settlement_time'] > 0 ?
                    date('Y-m-d H:i:s', $order_info['settlement_time']) : '';
            }
            if (isset($order_info['update_time'])) {
                $order_info['update_time'] = is_numeric($order_info['update_time']) ?
                    date('Y-m-d H:i:s', $order_info['update_time']) : $order_info['update_time'];
            }

            $lists['detal']=$order_info;
            $lists['jcai']=[];
            $lists['jcai_info']=[];
            if($lists['detal']['status']==3){
                $lists['detal']['freeze_status']=0;
            }
            if($lists['detal']['status']==1){
                $lists['detal']['freeze_status']=1;//只有未结算才能冻结
            }
            // 获取代理用户信息
            $agent_user = Db::name('user')->where(['id' => $lists['detal']['user_id']])->find();
            if ($agent_user) {
                $lists['detal']['nickname'] = $agent_user['nickname'];
                $lists['detal']['avatar'] = empty($agent_user['avatar']) ? '' : UrlServer::getFileUrl($agent_user['avatar']);
                $lists['detal']['agent_sn'] = $agent_user['sn'];
            } else {
                $lists['detal']['nickname'] = '未知代理';
                $lists['detal']['avatar'] = '';
                $lists['detal']['agent_sn'] = '';
            }

            if($lists['detal']['order_type']==1){
                $lang_type=['单月','一季','一年','两年','终身'];
                $lists['jcai']=Db::name('jcai_order')->where(['id'=>$lists['detal']['commissions_id']])->find();
                if ($lists['jcai']) {
                    $lists['jcai']['give_type']=JcaiTemplate::where(['id'=>$lists['jcai']['template_id']])->value('give_type');
                    $lists['jcai']['give_type']=$lang_type[$lists['jcai']['give_type']] ?? '未知';
                }
                $lists['jcai_info']=Db::name('user')->where(['id'=> $lists['jcai']['user_id']])->find();
            }else{
                $order_type=[1=>'用户集采购会员',2=>'商家入驻费',3=>'商家检验费',4=>'入驻及检验费组合'];
                $lists['detal']['order_type_text']=$order_type[$lists['detal']['order_type']];
                $lists['shop_fees']=Db::name('shop_merchantfees')->where(['id'=>$lists['detal']['commissions_id']])->find();
                $lists['shop']=$lists['shop_fees']['shop_id']?StoreLogic::detail($lists['shop_fees']['shop_id']):'';
                $lists['shop_user_info']=Db::name('user')->where(['id'=> $lists['shop_fees']['user_id']])->find();
            }

            return $lists;
        } catch (\Exception $e) {
            return ['error'=>$e->getMessage()];
        }
    }




    //佣金提现
    public static function cashApply($post){
        $agent_id=Agent::findOrEmpty($post['user_id']);
        if($agent_id->is_freeze==1){
            return ['error'=>'代理已被冻结，无法提现'];
        }
        //判断是否有未审批的提现


//        `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
//         `sn` varchar(64) NOT NULL DEFAULT '' COMMENT '提现单号',
//         `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商家ID',
//         `type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '提现类型 0银行卡 10支付宝',
//         `bank_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '银行卡账号ID',
//         `alipay_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '支付宝账号id',
//         `apply_amount` decimal(8,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '申请提现金额',
//         `left_amount` decimal(8,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '实际到账金额(扣除手续费)',
//         `poundage_amount` decimal(8,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '手续费金额',
//         `poundage_ratio` double(5,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '手续费比例',
//         `explainexplain` varchar(255) NOT NULL DEFAULT '' COMMENT '提现说明',
//         `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '提现状态[0=申请中, 1=处理中, 2=转账成功, 3=转账失败]',
//         `transfer_voucher` varchar(255) NOT NULL DEFAULT '' COMMENT '转账凭证(图)',
//         `transfer_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '转账时间',
//         `transfer_content` varchar(255) NOT NULL DEFAULT '' COMMENT '转账说明',
//         `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
//         `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
        try {
            $data['sn']=createSn('agent_withdrawal','sn');
            $data['user_id']=$post['user_id'];
            $data['agent_id']=Db::name('agent')->where(['user_id'=>$post['user_id']])->value('id');
            $data['type']=$post['type'];
            $data['bank_id']=$post['bank_id']??0;
            $data['alipay_id']=$post['alipay_id']??0;
            $data['apply_amount']=$post['apply_amount'];
            $data['left_amount']=$post['left_amount'];
            $data['poundage_amount']=$post['poundage_amount'];
            $data['poundage_ratio']=$post['poundage_ratio'];
            $data['explain']='代理线上申请提现';
            $data['status']=0;
            $data['create_time']=time();
            $data['update_time']=time();
            $res=Db::name('agent_withdrawal')->insertGetId($data);
            if(!$res){
                throw new \Exception('提现申请失败');
            }
            return $res;
        }catch (\Exception $e){
            return ['error'=>$e->getMessage()];
        }
    }




    /**
     * @Notes: 审核提现
     * @Author: 张无忌
     * @param $post
     * @return bool
     */
    public static function examine($post)
    {
        try {
            if ($post['is_examine']) {
                // 同意提现
                AgentWithdrawal::update([
                    'explain'     => $post['explain'] ?? '',
                    'status'      => WithdrawalEnum::HANDLE_STATUS,
                    'update_time' => time()
                ], ['id'=>$post['id']]);

            } else {
                // 拒绝提现
                // 拒绝提现，设置状态为提现失败(3)
                AgentWithdrawal::update([
                    'explain'     => $post['explain'] ?? '',
                    'status'      => 3, // WithdrawalEnum::ERROR_STATUS
                    'update_time' => time()
                ], ['id'=>$post['id']]);
            }
            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @Notes: 审核提现转账
     * @Author: 张无忌
     * @param $post
     * @return bool
     */
    public static function transfer($post)
    {
        try {
            if ($post['is_examine']) {
                // 转账成功
                AgentWithdrawal::update([
                    'transfer_content' => $post['transfer_content'] ?? '',
                    'status'           => WithdrawalEnum::SUCCESS_STATUS,
                    'transfer_voucher' => $post['image'] ?? '',
                    'transfer_time'    => time(),
                    'update_time'      => time()
                ], ['id'=>(int)$post['id']]);



            } else {
                // 转账失败
                // 转账失败，设置状态为提现失败(3)
                AgentWithdrawal::update([
                    'transfer_content' => $post['transfer_content'] ?? '',
                    'status'           => 3, // WithdrawalEnum::ERROR_STATUS
                    'transfer_voucher' => $post['image'] ?? '',
                    'transfer_time'    => time(),
                    'update_time'      => time()
                ], ['id'=>$post['id']]);

            }

            return true;
        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }



    static function transfer_online($post) : bool
    {

        try {

            $detail = AgentWithdrawal::with([ 'alipay' ])->findOrEmpty($post['id'])->toArray();

            $result = (new YansongdaAliPayTransferServer())->shopWithdrawTransfer($detail);

            if (true === $result) {
                // 转账成功
                AgentWithdrawal::update([
                    'explain'          => '',
                    'status'           => WithdrawalEnum::SUCCESS_STATUS,
                    'transfer_voucher' => '',
                    'transfer_time'    => time(),
                    'update_time'      => time()
                ], [ 'id' => (int) $post['id'] ]);

            } else {
                static::$error = (string) $result;

                // 转账失败
                // 支付宝转账失败，设置状态为提现失败(3)
                AgentWithdrawal::update([
                    'explain'          => '支付宝转账失败',
                    'status'           => 3, // WithdrawalEnum::ERROR_STATUS
                    'transfer_voucher' => '',
                    'transfer_time'    => time(),
                    'update_time'      => time()
                ], [ 'id' => $post['id'] ]);
            }

            return true;
        } catch (\Throwable $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @notes 代理保证金列表
     * @param array $params 查询参数
     * @return array
     * <AUTHOR> @date
     */
    public static function depositList($params)
    {
        $where = [];

        // 默认只显示status大于0的记录（已支付的保证金）
        // 除非明确搜索status=0的记录
        if (!isset($params['status']) || $params['status'] !== '0') {
            $where[] = ['status', '>', 0];
        }

        // 用户信息搜索
        if (isset($params['keyword']) && !empty($params['keyword'])) {
            $userIds = User::where('sn|nickname', 'like', '%' . $params['keyword'] . '%')
                ->column('id');
            if (!empty($userIds)) {
                $where[] = ['user_id', 'in', $userIds];
            } else {
                // 如果没有找到匹配的用户，返回空结果
                return [
                    'count' => 0,
                    'lists' => []
                ];
            }
        }

        // 保证金状态
        if (isset($params['status']) && $params['status'] !== '') {
            // 处理特殊状态：退款中(公示期)
            if ($params['status'] === '2_refund') {
                $where[] = ['status', '=', 2]; // 状态为2
                $where[] = ['refund_request_time', '>', 0]; // 有退款申请时间

                // 获取配置的退款公示期天数
                $config = ConfigServer::get('agent_setting', '', []);
                // 确保 refund_publicity_period_days 存在，如果不存在则设置默认值为 90
                if (!isset($config['refund_publicity_period_days'])) {
                    $config['refund_publicity_period_days'] = 90;
                }

                // 添加时间条件：当前时间 < 退款申请时间 + 公示期天数
                $currentTime = time();
                $publicityDays = intval($config['refund_publicity_period_days']);
                $where[] = ['refund_request_time', 'exp', Db::raw("+ {$publicityDays} * 86400 > {$currentTime}")];
            } else {
                if($params['status'] == 4){
                    $where[] = ['refund_time', '>', 0];
                }else{
                    $where[] = ['status', '=', $params['status']];
                }


            }
        }

        // 查询保证金记录
        $lists = AgentMerchantfees::where($where)
            ->order('id', 'desc')
            ->page($params['page'], $params['limit'])
            ->select()
            ->toArray();

        $count = AgentMerchantfees::where($where)->count();

        // 获取配置的退款公示期天数
        $config = ConfigServer::get('agent_setting', '', []);
        // 确保 refund_publicity_period_days 存在，如果不存在则设置默认值为 90
        if (!isset($config['refund_publicity_period_days'])) {
            $config['refund_publicity_period_days'] = 90;
        }

        // 补充用户信息和公示期信息
        foreach ($lists as &$item) {
            // 添加退款公示期天数
            $item['refund_publicity_period_days'] = $config['refund_publicity_period_days'];

            // 计算退款公示期结束时间
            if (isset($item['refund_request_time']) && is_numeric($item['refund_request_time']) && $item['refund_request_time'] > 0) {
                $item['refund_publicity_end_time'] = intval($item['refund_request_time']) + (intval($config['refund_publicity_period_days']) * 86400);
            } else {
                $item['refund_publicity_end_time'] = 0;
            }

            // 计算当前余额
            try {
                $current_balance = AgentDepositLogic::calculateCurrentBalance($item['id']);
                $item['current_balance'] = number_format(floatval($current_balance), 2);
            } catch (\Exception $e) {
                $item['current_balance'] = '0.00';
            }

            $item['payment_date']=date('Y-m-d H:i:s',$item['payment_date']);
             $item['refund_time']=date('Y-m-d H:i:s',$item['refund_time']);
            $item['refund_request_time']=date('Y-m-d H:i:s',$item['refund_request_time']);
            // 获取用户信息
            $user = User::where('id', $item['user_id'])->find();
            if ($user) {
                $item['user_sn'] = $user['sn'];
                $item['nickname'] = $user['nickname'];
                $item['avatar'] = empty($user['avatar']) ? '' : UrlServer::getFileUrl($user['avatar']);
            } else {
                $item['user_sn'] = '';
                $item['nickname'] = '未知用户';
                $item['avatar'] = '';
            }
        }

        return [
            'count' => $count,
            'lists' => $lists
        ];
    }

    /**
     * @notes 代理保证金退款
     * @param int $id 保证金记录ID
     * @param int $adminId 管理员ID
     * @return bool
     * <AUTHOR> @date
     */
    public static function refundDeposit($id, $adminId)
    {
        Db::startTrans();
        try {
            // 查询保证金记录
            $deposit = AgentMerchantfees::where('id', $id)->find();
            if (!$deposit) {
                throw new \Exception('保证金记录不存在');
            }

            // 检查状态是否为退款申请中
            if ($deposit['status'] != 3) {
                throw new \Exception('只有退款申请中的保证金才能进行退款操作');
            }

            // 获取用户信息
            $user = User::where('id', $deposit['user_id'])->find();
            if (!$user) {
                throw new \Exception('用户不存在');
            }

            $refund=Db::name('common_refund')->where('source_id', $id)->where('refund_type', 4)->find();
            if($refund && $refund['refund_status'] == 1){
                throw new \Exception('该订单已退款');
            }
            // 执行退款操作
            // 检查是否已有退款记录
            $refund = Db::name('common_refund')
                ->where('source_id', $id)
                ->where('refund_type', 4) // 4-代理保证金
                ->find();

            // 如果没有退款记录，创建一个
            if (!$refund) {
                // 获取支付方式信息（假设默认为微信支付）
                $payment_method = PayEnum::WECHAT_PAY;

                // 创建退款记录
                $refund_data = [
                    'refund_sn' => createSn('common_refund', 'refund_sn'),
                    'refund_type' => 4, // 4-代理保证金
                    'source_id' => $id,
                    'user_id' => $deposit['user_id'],
                    'agent_id' => Db::name('agent')->where('user_id', $deposit['user_id'])->value('id') ?? 0,
                    'refund_amount' => $deposit['amount'],
                    'total_amount' => $deposit['amount'],
                    'payment_method' => $payment_method,
                    'transaction_id' => '', // 可能需要从其他表获取
                    'refund_status' => 0, // 0-退款中
                    'admin_id' => $adminId,
                    'remark' => '代理保证金退款',
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $refund_id = Db::name('common_refund')->insertGetId($refund_data);
                $refund = Db::name('common_refund')->where('id', $refund_id)->find();
            }

            // 判断支付时间是否超过1年（微信支付退款期限）
            $one_year_ago = time() - 365 * 24 * 60 * 60; // 一年前的时间戳
            $is_expired = $deposit['payment_date'] < $one_year_ago;

            // 如果已经超过1年，尝试使用支付宝转账
            if ($is_expired) {
                // 查询代理的支付宝账户
                $agent_id = Db::name('agent')->where('user_id', $deposit['user_id'])->value('id');
                $alipay = Db::name('agent_alipay')
                    ->where('agent_id', $agent_id)
                    ->where('del', 0)
                    ->find();

                // 如果有支付宝账户，使用支付宝转账
                if ($alipay) {
                    // 创建一个临时的转账数据结构，用于支付宝转账
                    $transfer_data = [
                        'sn' => $refund['refund_sn'],
                        'left_amount' => $deposit['amount'],
                        'alipay' => [
                            'account' => $alipay['account'],
                            'username' => $alipay['username']
                        ]
                    ];

                    // 执行支付宝转账
                    $result = (new YansongdaAliPayTransferServer())->shopWithdrawTransfer($transfer_data);

                    if ($result === true) {
                        // 转账成功，更新退款记录
                        Db::name('common_refund')
                            ->where('id', $refund['id'])
                            ->update([
                                'refund_status' => 1, // 1-退款成功
                                'refund_msg' => '支付宝转账成功',
                                'updated_at' => date('Y-m-d H:i:s')
                            ]);

                        // 更新用户代理状态
                        Db::name('user')->where('id', $deposit['user_id'])->update(['is_agent'=>0,'agent_id'=>0]);

                        // 更新代理表的del字段为1（标记为删除）
                        Db::name('agent')->where('user_id', $deposit['user_id'])->update(['del' => 1]);

                        // 更新保证金状态
                        AgentMerchantfees::where('id', $id)->update([
                            'status' => 3, // 已退款
                            'refund_time' => time()
                        ]);

                        // 记录操作日志
                        Db::name('agent_deposit_log')->insert([
                            'deposit_id' => $id,
                            'user_id' => $deposit['user_id'],
                            'admin_id' => $adminId,
                            'action' => '退款',
                            'remark' => '管理员执行保证金退款操作(支付宝转账)',
                            'create_time' => time()
                        ]);
                        Db::name('user')->where('id', $deposit['user_id'])->update(['is_agent'=>0,'agent_id'=>0]);
                        Db::commit();
                        return true;
                    } else {
                        // 转账失败，更新退款记录
                        Db::name('common_refund')
                            ->where('id', $refund['id'])
                            ->update([
                                'refund_status' => 2, // 2-退款失败
                                'refund_msg' => '支付宝转账失败: ' . (is_string($result) ? $result : json_encode($result, JSON_UNESCAPED_UNICODE)),
                                'updated_at' => date('Y-m-d H:i:s')
                            ]);

                        // 更新保证金状态
                        AgentMerchantfees::where('id', $id)->update([
                            'status' => 5, // 退款失败
                        ]);

                        throw new \Exception('支付宝转账失败: ' . (is_string($result) ? $result : json_encode($result, JSON_UNESCAPED_UNICODE)));
                    }
                } else {
                    // 没有支付宝账户，获取银行卡信息
                    $bank_cards = Db::name('agent_bank')
                        ->where('agent_id', $agent_id)
                        ->where('del', 0)
                        ->select()
                        ->toArray();

                    // 更新退款记录
                    Db::name('common_refund')
                        ->where('id', $refund['id'])
                        ->update([
                            'refund_status' => 2, // 2-退款失败
                            'refund_msg' => '微信支付退款已过期且未找到支付宝账户，需要手动转账',
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

                    // 更新保证金状态
                    AgentMerchantfees::where('id', $id)->update([
                        'status' => 5, // 退款失败
                    ]);

                    // 记录操作日志
                    Db::name('agent_deposit_log')->insert([
                        'deposit_id' => $id,
                        'user_id' => $deposit['user_id'],
                        'admin_id' => $adminId,
                        'action' => '退款失败',
                        'remark' => '微信支付退款已过期且未找到支付宝账户，需要手动转账',
                        'create_time' => time()
                    ]);

                    // 返回银行卡信息
                    self::$error = '微信支付退款已过期且未找到支付宝账户，需要手动转账到银行卡';
                    Db::rollback();
                    return ['bank_cards' => $bank_cards, 'error' => self::$error];
                }
            } else {
                // 未超过1年，使用微信支付退款
                try {
                    // 获取微信支付配置
                    $pay = new \app\common\model\Pay();
                    $payConfig = $pay->where(['code' => 'wechat'])->find();
                    if (!$payConfig) {
                        throw new \Exception('未找到微信支付配置');
                    }

                    // 获取微信小程序配置
                    $mnpConfig = \app\common\server\ConfigServer::get('mnp');

                    // 构建微信支付配置数组
                    $certPath = public_path() . ($payConfig['config']['apiclient_cert'] ?? '');
                    $keyPath = public_path() . ($payConfig['config']['apiclient_key'] ?? '');

                    // 检查证书文件是否存在
                    if (!file_exists($certPath)) {
                        throw new \Exception('微信支付证书文件不存在: ' . $certPath);
                    }

                    if (!file_exists($keyPath)) {
                        throw new \Exception('微信支付密钥文件不存在: ' . $keyPath);
                    }

                    // 记录配置信息
                    Db::name('log')->insert([
                        'type' => 'refund_config',
                        'log' => json_encode([
                            'pay_config' => $payConfig,
                            'mnp_config' => $mnpConfig
                        ], JSON_UNESCAPED_UNICODE),
                        'creat_time' => date('Y-m-d H:i:s')
                    ]);

                    // 确保有app_id
                    $app_id = '';
                    if (!empty($payConfig['config']['app_id'])) {
                        $app_id = $payConfig['config']['app_id'];
                    } elseif (!empty($mnpConfig['app_id'])) {
                        $app_id = $mnpConfig['app_id'];
                    } else {
                        throw new \Exception('未找到微信支付AppID，请检查配置');
                    }

                    $config = [
                        'app_id' => $app_id,
                        'mch_id' => $payConfig['config']['mch_id'] ?? '',
                        'key' => $payConfig['config']['pay_sign_key'] ?? '',
                        'cert_path' => $certPath,
                        'key_path' => $keyPath,
                        'response_type' => 'array',
                        'log' => [
                            'level' => 'debug',
                            'file' => app()->getRootPath() . 'runtime/wechat/' . date('Ym') . '/' . date('d') . '.log'
                        ],
                    ];

                    // 尝试获取交易号
                    $transaction_id = $refund['transaction_id'];
                    if (empty($transaction_id)) {
                        // 如果退款记录中没有交易号，尝试从支付记录中获取
                        throw new \Exception('未找到微信支付交易号，无法进行退款');

                    }

                    // 准备退款数据
                    $data = [
                        'transaction_id' => $transaction_id,
                        'refund_sn' => $refund['refund_sn'],
                        'total_fee' => bcmul($deposit['amount'], 100), // 订单金额,单位为分
                        'refund_fee' => bcmul($deposit['amount'], 100), // 退款金额,单位为分
                    ];

                    // 调用微信退款接口
                    $result = \app\common\server\WeChatPayServer::refund($config, $data);

                    if (isset($result['return_code']) && $result['return_code'] == 'FAIL') {
                        // 退款失败，更新退款记录
                        Db::name('common_refund')
                            ->where('id', $refund['id'])
                            ->update([
                                'refund_status' => 2, // 2-退款失败
                                'refund_msg' => json_encode($result, JSON_UNESCAPED_UNICODE),
                                'updated_at' => date('Y-m-d H:i:s')
                            ]);

                        // 更新保证金状态
                        AgentMerchantfees::where('id', $id)->update([
                            'status' => 5, // 退款失败
                        ]);

                        throw new \Exception('微信支付退款失败: ' . $result['return_msg']);
                    }

                    // 退款成功，更新退款记录
                    Db::name('common_refund')
                        ->where('id', $refund['id'])
                        ->update([
                            'refund_status' => 1, // 1-退款成功
                            'refund_msg' => json_encode($result, JSON_UNESCAPED_UNICODE),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

                    // 更新用户代理状态
                    Db::name('user')->where('id', $deposit['user_id'])->update(['is_agent'=>0,'agent_id'=>0]);

                    // 更新代理表的del字段为1（标记为删除）
                    Db::name('agent')->where('user_id', $deposit['user_id'])->update(['del' => 1]);

                    // 更新保证金状态
                    AgentMerchantfees::where('id', $id)->update([
                        'status' =>3, // 已退款
                        'refund_time' => time()
                    ]);
                } catch (\Exception $e) {
                    // 捕获异常，更新退款记录
                    Db::name('common_refund')
                        ->where('id', $refund['id'])
                        ->update([
                            'refund_status' => 2, // 2-退款失败
                            'refund_msg' => $e->getMessage(),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

                    // 更新保证金状态
                    AgentMerchantfees::where('id', $id)->update([
                        'status' => 5, // 退款失败
                    ]);

                    throw $e;
                }
            }


            // 记录操作日志
            Db::name('agent_deposit_log')->insert([
                'deposit_id' => $id,
                'user_id' => $deposit['user_id'],
                'admin_id' => $adminId,
                'action' => '退款',
                'remark' => '管理员执行保证金退款操作',
                'create_time' => time()
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @notes 获取保证金详情
     * @param int $id 保证金记录ID
     * @return array
     * <AUTHOR> @date
     */
    public static function getDepositDetail($id)
    {
        // 查询保证金记录
        $deposit = AgentMerchantfees::where('id', $id)->find();
        if (!$deposit) {
            return [];
        }

        // 转换为数组
        $deposit = $deposit->toArray();

        // 处理时间字段映射 - 数据库中使用的是 created_at 而不是 create_time
        if (isset($deposit['created_at'])) {
            $deposit['create_time'] = $deposit['created_at']; // 映射字段名
        } else {
            $deposit['create_time'] = '';
        }

        // 格式化时间字段
        if (isset($deposit['payment_date'])) {
            $deposit['payment_date'] = is_numeric($deposit['payment_date']) && $deposit['payment_date'] > 0 ?
                date('Y-m-d H:i:s', $deposit['payment_date']) : '';
        } else {
            $deposit['payment_date'] = '';
        }
      
        if (isset($deposit['refund_request_time'])) {
            $deposit['refund_request_time'] = is_numeric($deposit['refund_request_time']) && $deposit['refund_request_time'] > 0 ?
                date('Y-m-d H:i:s', $deposit['refund_request_time']) : '';
        } else {
            $deposit['refund_request_time'] = '';
        }

        if (isset($deposit['refund_time'])) {
            $deposit['refund_time'] = is_numeric($deposit['refund_time']) && $deposit['refund_time'] > 0 ?
                date('Y-m-d H:i:s', $deposit['refund_time']) : '';
        } else {
            $deposit['refund_time'] = '';
        }

        // 确保所有必要字段都有默认值（这些字段在数据库中可能不存在）
        $deposit['remark'] = $deposit['remark'] ?? ''; // 数据库中没有此字段
        $deposit['payment_method'] = $deposit['payment_method'] ?? '微信支付'; // 数据库中没有此字段，设置默认值
        $deposit['transaction_id'] = $deposit['transaction_id'] ?? '';

        // 获取用户信息
        $user = User::where('id', $deposit['user_id'])->find();
        if ($user) {
            $deposit['user_sn'] = $user['sn'];
            $deposit['user_nickname'] = $user['nickname'];
            $deposit['user_avatar'] = empty($user['avatar']) ? '' : UrlServer::getFileUrl($user['avatar']);
        } else {
            $deposit['user_sn'] = '';
            $deposit['user_nickname'] = '未知用户';
            $deposit['user_avatar'] = '';
        }

        // 计算当前余额
        try {
            $current_balance = AgentDepositLogic::calculateCurrentBalance($deposit['id']);
            $deposit['current_balance'] = number_format(floatval($current_balance), 2);
        } catch (\Exception $e) {
            $deposit['current_balance'] = '0.00';
        }

        // 获取配置的退款公示期天数
        $config = ConfigServer::get('agent_setting', '', []);
        if (!isset($config['refund_publicity_period_days'])) {
            $config['refund_publicity_period_days'] = 90;
        }

        // 计算退款公示期结束时间
        if (isset($deposit['refund_request_time']) && is_numeric($deposit['refund_request_time']) && $deposit['refund_request_time'] > 0) {
            $deposit['refund_publicity_end_time'] = intval($deposit['refund_request_time']) + (intval($config['refund_publicity_period_days']) * 86400);
        } else {
            $deposit['refund_publicity_end_time'] = 0;
        }

        return $deposit;
    }

    /**
     * @notes 代理保证金手动退款（银行卡/支付宝）
     * @param array $data 手动退款数据
     * @return bool
     * <AUTHOR> @date
     */
    public static function manualRefund($data)
    {
        Db::startTrans();
        try {
            // 查询保证金记录
            $deposit = AgentMerchantfees::where('id', $data['id'])->find();
            if (!$deposit) {
                throw new \Exception('保证金记录不存在');
            }

            // 检查状态是否为退款申请中或退款失败
            if ($deposit['status'] != 3 && $deposit['status'] != 5) {
                throw new \Exception('只有退款申请中或退款失败的保证金才能进行手动退款操作');
            }

            // 获取用户信息
            $user = User::where('id', $deposit['user_id'])->find();
            if (!$user) {
                throw new \Exception('用户不存在');
            }

            // 获取退款记录
            $refund = Db::name('common_refund')->where('id', $data['refund_id'])->find();

            // 如果退款记录不存在，则创建一条新的退款记录
            if (!$refund) {
                // 创建退款记录
                $refundData = [
                    'id' => $data['refund_id'],
                    'refund_sn' => 'RF' . date('YmdHis') . mt_rand(1000, 9999),
                    'refund_type' => 4, // 4-代理保证金
                    'source_id' => $data['id'],
                    'user_id' => $deposit['user_id'],
                    'refund_amount' => $deposit['amount'],
                    'total_amount' => $deposit['amount'],
                    'payment_method' => $deposit['payment_method'] ?? '微信支付',
                    'transaction_id' => $deposit['transaction_id'] ?? '',
                    'refund_status' => 0, // 0-退款中
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $result = Db::name('common_refund')->insert($refundData);
                if (!$result) {
                    throw new \Exception('创建退款记录失败');
                }

                // 重新获取退款记录
                $refund = Db::name('common_refund')->where('id', $data['refund_id'])->find();
                if (!$refund) {
                    throw new \Exception('获取退款记录失败');
                }
            }

            // 检查退款状态
            if ($refund && $refund['refund_status'] == 1) {
                throw new \Exception('该订单已退款');
            }

            // 根据支付方式处理
            $manual_refund_type = $data['payment_type'];
            $bank_id = null;
            $alipay_id = null;

            if ($manual_refund_type == 1) { // 银行卡
                $bank_id = $data['bank_id'];
                // 检查银行卡是否存在
                $bank = Db::name('agent_bank')->where('id', $bank_id)->find();
                if (!$bank) {
                    throw new \Exception('银行卡信息不存在');
                }
            } else if ($manual_refund_type == 2) { // 支付宝
                $alipay_id = $data['alipay_id'];
                // 检查支付宝账户是否存在
                $alipay = Db::name('agent_alipay')->where('id', $alipay_id)->find();
                if (!$alipay) {
                    throw new \Exception('支付宝账户信息不存在');
                }
            } else {
                throw new \Exception('不支持的支付方式');
            }

            // 更新退款记录
            Db::name('common_refund')
                ->where('id', $refund['id'])
                ->update([
                    'refund_status' => 1, // 1-退款成功
                    'refund_msg' => '管理员手动退款成功',
                    'transfer_voucher' => $data['transfer_voucher'],
                    'manual_refund_type' => $manual_refund_type,
                    'bank_id' => $bank_id,
                    'alipay_id' => $alipay_id,
                    'manual_refund_time' => date('Y-m-d H:i:s'),
                    'remark' => $data['remark'] ?? '',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            // 更新用户代理状态
            Db::name('user')->where('id', $deposit['user_id'])->update(['is_agent'=>0,'agent_id'=>0]);

            // 更新代理表的del字段为1（标记为删除）
            Db::name('agent')->where('user_id', $deposit['user_id'])->update(['del' => 1]);

            // 更新保证金状态
            AgentMerchantfees::where('id', $data['id'])->update([
                'status' => 4, // 已退款
                'refund_time' => time()
            ]);

            // 记录操作日志
            Db::name('agent_deposit_log')->insert([
                'deposit_id' => $data['id'],
                'user_id' => $deposit['user_id'],
                'admin_id' => $data['admin_id'],
                'action' => '手动退款',
                'remark' => '管理员执行保证金手动退款操作',
                'create_time' => time()
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }
}
